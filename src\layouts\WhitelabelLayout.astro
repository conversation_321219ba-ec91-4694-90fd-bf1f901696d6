---
import Layout from "@layouts/Layout.astro";
import { Image } from "astro:assets";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

// Components
import CTA from "@components/whitelabel/CTA.astro";
import TestsList from "@components/whitelabel/TestsList.astro";

// Images
import HeroImg from "@assets/images/whitelabel/whitelabel-hero.png";
import JuniCoachLogo from "@assets/images/junicoach-logo.svg";

import JunicoachRecordRu from "@assets/images/whitelabel/junicoach-record-ru.png";
import JunicoachResultTestRu from "@assets/images/whitelabel/junicoach-result-test-ru.png";

import PlayersCardsRu from "@assets/images/whitelabel/whitelabel-players-cards-ru.png";

// How
import ConesImg from "@assets/images/whitelabel/how/cones.jpg";
import PhoneImg from "@assets/images/whitelabel/how/phone.jpg";
import TapeImg from "@assets/images/whitelabel/how/tape.jpg";
import TripodImg from "@assets/images/whitelabel/how/tripod.jpg";
import EquipPhotoImg from "@assets/images/whitelabel/how/equip-photo.jpg";

// Icons
import SolarTShirtBold from "~icons/solar/t-shirt-bold";
import SolarDevicesBoldDuotone from "~icons/solar/devices-bold-duotone";
import SolarCrownMinimalisticBoldDuotone from "~icons/solar/crown-minimalistic-bold-duotone";
import SolarCode2BoldDuotone from "~icons/solar/code-2-bold-duotone";
import SolarVerifiedCheckBold from "~icons/solar/verified-check-bold";
import SolarVideocameraAddBold from "~icons/solar/videocamera-add-bold";
import SolarChartBold from "~icons/solar/chart-bold";
import SolarShieldCheckBold from "~icons/solar/shield-check-bold";
import SolarCalendarAddBold from "~icons/solar/calendar-add-bold";
---

<Layout title="Whitelabel-решения для AI-тестирования футболистов" contentOnly autoChangeLang={false}>
  <!-- Hero -->
  <section class="">
    <header class="mt-8">
      <div class="container">
        <Image src={JuniCoachLogo} alt="JuniCoach" class="w-auto h-9" />
      </div>
    </header>

    <div class="z-10 relative mt-8 max-lg:mt-10 container">
      <div
        class="flex max-md:flex-col justify-between md:items-center md:gap-4 min-h-[80vh]"
      >
        <div class="flex flex-col items-start max-w-screen-lg lg:max-w-[40rem]">
          <!-- Badge -->
          <div
            class="flex items-center gap-2 bg-bg-accent px-3 py-2 border border-white rounded-full font-medium text-sm"
          >
            <SolarTShirtBold class="size-6" />
            <span>100+ клубов и академий</span>
          </div>

          <!-- Headline -->
          <h1 class="mt-4 font-bold text-3xl md:text-5xl xl:text-6xl">
            Whitelabel-решения для AI-тестирования футболистов
          </h1>
          <p class="mt-4 lg:mt-8 text-base lg:text-xl xl:text-2xl">
            Собственный брендированный раздел с батареей тестов в приложении
            JuniCoach, с логотипом и цветами вашего клуба при заказе
            от 10 000 тестов
          </p>

          <!-- CTA -->
          <CTA class="mt-8 lg:mt-14" />
        </div>

        <Image
          src={HeroImg}
          alt=""
          class="max-md:mt-10 max-w-[30.75rem] xl:max-w-[38.75rem] object-contain"
        />
      </div>
    </div>
  </section>

  <!-- Cards -->
  <section class="mt-14">
    <div class="container">
      <div class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.7fr_0.7fr]">
        {
          [
            {
              text: "Доступ тренеров и скаутов к результатам в личном кабинете и мобильном приложении",
              icon: SolarDevicesBoldDuotone,
            },
            {
              text: "Тесты по специальной цене при контрактах от 2 млн ₽",
              icon: SolarCrownMinimalisticBoldDuotone,
            },
            {
              text: "Возможность разработки тестов под требования клуба",
              icon: SolarCode2BoldDuotone,
            },
          ].map((card) => {
            return (
              <div class="flex flex-col items-start gap-4 bg-white p-6 rounded-2xl w-full text-lg xl:text-2xl">
                <div class="p-4 rounded-2xl bg-accent-primary">
                  <card.icon class="size-10 lg:size-16" />
                </div>
                <p>{card.text}</p>
              </div>
            );
          })
        }
      </div>
    </div>
  </section>

  <!-- Technology -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.9fr] lg:grid-rows-auto"
      >
        <!-- Content cell -->
        <div class="items-start max-lg:gap-4 max-lg:grid md:grid-cols-2">
          <div>
            <h2 class="font-bold text-2xl/tight lg:text-5xl">
              Передовая технология тестирования и скаутинга в цветах клуба
            </h2>
            <p class="mt-6 lg:mt-8 text-base lg:text-xl xl:text-2xl">
              Используя только мобильные телефоны и штативы тренеры могут быстро
              тестировать и получать точные данные футболистов, сравнивать
              их с нормативами, определять и развивать таланты
            </p>
          </div>
          <!-- Recommend -->
          <div
            class="flex max-lg:flex-col lg:items-center gap-4 lg:mt-10 p-4 rounded-2xl text-white bg-accent-success"
          >
            <SolarVerifiedCheckBold class="size-16 lg:size-32" />
            <div>
              <div class="font-medium text-lg/tight lg:text-xl xl:text-2xl">
                Проверена специалистами РФС и применяется для комплексной оценки
                игроков
              </div>
              <a href="" class="block mt-2 underline hover:no-underline"
                >Рекомендательное письмо ↗</a
              >
            </div>
          </div>
        </div>

        <!-- Images -->
        <Image
          src={JunicoachResultTestRu}
          alt=""
          class="max-lg:hidden row-span-2 lg:max-w-[32rem]"
        />
        <Image
          src={JunicoachRecordRu}
          alt=""
          class="max-md:w-[150vw] max-w-full max-md:max-w-none"
        />
      </div>
    </div>
  </section>

  <!-- Equipment -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        Тестирование без дорогого оборудования, в любом месте
      </h2>
      <div class="gap-6 grid md:grid-cols-2 mt-12">
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarVideocameraAddBold class="size-10 lg:size-16" />
          </div>
          <span>Тренер снимает видео по инструкции в приложении</span>
        </div>
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarChartBold class="size-10 lg:size-16" />
          </div>
          <span
            >Платформа мгновенно обрабатывает данные и выдаёт точные показатели
            игроков
          </span>
        </div>
      </div>
      <div class="gap-6 grid md:grid-cols-2 mt-6">
        <div
          class="max-lg:items-center gap-4 grid grid-cols-[auto_1fr] lg:grid-cols-2 grid-rows-2 max-lg:bg-white max-lg:p-4 max-lg:rounded-2xl"
        >
          {
            [
              {
                title: t("clubs.how.equip_item.phone"),
                img: PhoneImg,
              },
              {
                title: t("clubs.how.equip_item.tripod"),
                img: TripodImg,
              },
              {
                title: t("clubs.how.equip_item.cones"),
                img: ConesImg,
              },
              {
                title: t("clubs.how.equip_item.tape"),
                img: TapeImg,
              },
            ].map((equip) => {
              return (
                <div class="max-lg:contents flex max-lg:flex-col items-center lg:gap-4 lg:bg-white p-6 rounded-2xl w-full text-lg/tight">
                  <Image
                    src={equip.img}
                    alt={equip.title}
                    class="w-26 xl:w-32 h-auto"
                  />
                  <span class="">{equip.title}</span>
                </div>
              );
            })
          }
        </div>
        <CTA class="col-1" />
        <Image
          class="md:row-start-1 rounded-2xl w-full h-full object-cover md:col-2"
          src={EquipPhotoImg}
          alt="Coach tests Antalyaspor team via JuniCoach"
        />
      </div>
    </div>
  </section>

  <!-- Tests -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="mb-12 max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        Тестирование без дорогого оборудования, в любом месте
      </h2>
      <TestsList lang={lang} />
    </div>
  </section>

  <!-- Modern -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="gap-8 grid md:grid-cols-2 bg-white p-4 lg:p-10 rounded-2xl w-full"
      >
        <!-- Col 1 -->
        <div class="flex flex-col gap-6">
          <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
            Современный подход к отбору и развитию футболистов
          </h2>
          <ul class="space-y-2 ml-6 text-base lg:text-2xl list-disc">
            {
              [
                "Сравнивайте игроков с ровесниками по всему миру",
                "Делитесь профилями с родителями",
                "Подтверждайте решения тренеров с помощью объективных данных",
              ].map((listItem) => {
                return (
                  <li class="">
                    <span>{listItem}</span>
                  </li>
                );
              })
            }
          </ul>
          <CTA />

          <!-- Secure badge -->
          <div
            class="flex justify-center items-center gap-4 bg-success-50 p-2 md:px-4 border border-success-500 rounded-full text-base/tight"
          >
            <SolarShieldCheckBold class="size-6 text-accent-success shrink-0" />
            Безопасность и сохранность данных под строгим контролем
          </div>
        </div>
        <!-- Col 2 -->
        <div
          class="flex justify-center items-center p-2 lg:p-6 rounded-2xl bg-accent-primary"
        >
          <Image src={PlayersCardsRu} alt="Players cards" />
        </div>
      </div>
    </div>
  </section>

  <!-- Media about us -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        СМИ о нас
      </h2>

      <div class="gap-4 grid sm:grid-cols-2 lg:grid-cols-3 mt-12">
        {
          [
            {
              title: "Freedom QJ League провела цифровое тестирование игроков",
              url: "https://qjl.kz/ru/news/digitalisation-in-youth-football",
            },
            {
              title: "Программа цифровизации учеников «Зенит-Чемпионика»",
              url: "https://dzen.ru/a/ZcFY7XY11GsTjwDt",
            },
            {
              title: "Отбор игроков на фестиваль с помощью умного тестирования",
              url: "https://ngs.ru/text/gorod/2025/06/18/75597434/",
            },
          ].map((media) => {
            return (
              <div class="relative flex flex-col gap-4 bg-white p-4 rounded-2xl">
                <a
                  href={media.url}
                  target="_blank"
                  class="before:z-10 before:absolute before:inset-0 before:size-full"
                >
                  <span class="font-medium md:text-2xl">{media.title}</span>
                </a>

                <span class="text-sm">
                  {new URL(media.url).hostname.replace("www.", "")}
                </span>
              </div>
            );
          })
        }
      </div>
    </div>
  </section>

  <!-- CTA Banner -->
  <section class="mt-16 md:mt-28">
    <div class="container">
      <div
        class="flex flex-col justify-center items-center gap-6 p-4 md:p-12 py-6 rounded-2xl bg-accent-primary"
      >
        <h2 class="font-bold text-2xl/tight lg:text-5xl text-center">
          Подключите свой бренд в JuniCoach
        </h2>

        <div
          class="flex md:flex-row flex-col justify-center max-md:items-stretch gap-4 w-full"
        >
          <button
            class="flex justify-center items-center gap-4 bg-white hover:bg-white/80 p-4 rounded-2xl w-fit max-md:w-full font-medium text-lg/tight lg:text-2xl transition-colors"
          >
            <span>Связаться с менеджером</span>
            <SolarCalendarAddBold class="size-6 lg:size-12 shrink-0" />
          </button>
          <button
            class="flex justify-center items-center gap-4 bg-white/30 hover:bg-white/90 shadow-[inset_0_0_10px_#fff] p-4 border-2 border-white rounded-2xl w-fit max-md:w-full font-medium text-lg/tight lg:text-2xl transition-colors"
          >
            <span>Инструкция по работе с системой</span>
          </button>
        </div>
      </div>
    </div>
  </section>

  <footer class="mt-16 mb-10">
    <div class="text-center container">
      <p>ООО «Юнистат» © Москва</p>
    </div>
  </footer>
</Layout>
