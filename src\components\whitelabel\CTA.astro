---
import SolarCalendarAddBold from "~icons/solar/calendar-add-bold";
import { twMerge } from 'tailwind-merge'

const { class: classList } = Astro.props;
---

<div
  class={twMerge("flex flex-col items-stretch gap-2 lg:gap-4 bg-white p-2 lg:p-4 rounded-2xl w-full text-center", classList)}
>
  <button
    class="flex justify-center items-center gap-4 hover:bg-primary-500 p-4 rounded-2xl font-medium text-lg/tight lg:text-2xl transition-colors bg-accent-primary"
  >
    <span>Связаться с менеджером</span>
    <SolarCalendarAddBold class="size-6 lg:size-12 shrink-0" />
  </button>
  <span class="text-sm lg:text-base">
    Ответим на вопросы и предложим варианты интеграции
  </span>
</div>
