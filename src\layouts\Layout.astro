---
import "@styles/_variables.css";
import "@styles/global.css";

import AppHeader from "@components/AppHeader.astro";
import AppAchievementHeader from "@components/AppAchievementHeader.astro";
import AppFooter from "@components/AppFooter.astro";
import SupportBubble from "@components/SupportBubble.astro";
import AutoChangeLang from "@components/AutoChangeLang.astro";

import { getLangFromUrl, useTranslations } from "../lang/utils";

interface Props {
  title?: string;
  description?: string;
  noindex?: boolean;
  contentOnly?: boolean;
  ogImage?: any;
  chat?: boolean;
  hideAchievementHeader?: boolean;
  autoChangeLang?: boolean;
}

const {
  title,
  description,
  ogImage,
  noindex,
  contentOnly = false,
  hideAchievementHeader = false,
  chat = true,
  autoChangeLang = true,
} = Astro.props;

const setTitle = title ?? "JuniStat | Smart testing system";

const lang: string = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<!doctype html>
<html lang={lang}>
  <head itemscope itemtype="https://schema.org/WebPage">
    <meta charset="UTF-8" />
    <meta
      name="google-site-verification"
      content="xUS6qa2VAgiLrCEkCO_VUpJPKrSNVQwVPEE00WiTskU"
    />
    <!-- Disable indexing page -->
    {noindex && <meta name="robots" content="noindex, nofollow" />}
    <link rel="sitemap" href="/sitemap-index.xml" />

    <link rel="alternate" hreflang="ru" href="https://junistat.ru/" />
    <link rel="alternate" hreflang="en" href="https://junistat.com/" />
    <link rel="alternate" hreflang="x-default" href="https://junistat.com/" />
    <link rel="canonical" href="https://junistat.com/" />
    <meta name="og:title" content={setTitle} itemprop="headline" />

    <meta
      property="description"
      content={description ?? t("meta.description")}
      itemprop="description"
    />
    <meta
      name="keywords"
      property="keywords"
      content="Testing system, Юнистат, soccer trials, football trials, soocer test, football test, club testing, test players"
      itemprop="keywords"
    />

    <meta
      name="og:description"
      property="og:description"
      content={description ?? t("meta.description")}
    />
    <meta
      property="og:image"
      content={ogImage ?? "https://junistat.com/og-eng.jpg"}
      name="og:image"
    />
    <meta
      property="og:image:secure_url"
      content={ogImage ?? "https://junistat.com/og-eng.jpg"}
    />
    <meta name="og:type" content="website" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={setTitle} />
    <meta
      name="twitter:description"
      content={description ?? t("meta.description")}
    />
    <meta
      name="twitter:image"
      content={ogImage ?? "https://junistat.com/og-eng.jpg"}
    />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content={Astro.generator} />

    <!-- Favicon -->
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="https://junistat.com/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="https://junistat.com/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="https://junistat.com/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="https://junistat.com/favicon/site.webmanifest" />
    <link
      rel="mask-icon"
      href="https://junistat.com/favicon/safari-pinned-tab.svg"
      color="#4e5461"
    />
    <link rel="shortcut icon" href="https://junistat.com/favicon/favicon.ico" />
    <meta name="msapplication-TileColor" content="#ffe83f" />
    <meta
      name="msapplication-config"
      content="https://junistat.com/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#FFE83F" />

    <title>{setTitle}</title>

    <!-- Google Tag Manager -->
    <script is:inline src="/trackers.js"></script>
    <!-- End Google Tag Manager -->

    {autoChangeLang && <AutoChangeLang />}
  </head>
  <body>
    {!hideAchievementHeader && <AppAchievementHeader lang={lang} />}
    {!contentOnly && <AppHeader />}
    <slot />
    {!contentOnly && <AppFooter />}

    {chat && <SupportBubble />}
  </body>
</html>

<script>
  import "../utils/metrika.js";
  // import "../utils/autoChangeLang.ts";

  import { init } from "@amplitude/analytics-browser";
  // @ts-ignore
  init("a86a3e4b0431850651879b86c3f71ca2", null, {
    defaultTracking: true,
    saveEvents: true,
    includeUtm: true,
    includeReferrer: true,
  });
</script>
